# Python
__pycache__/
**/__pycache__/
*/__pycache__/*
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
.dist
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Project specific
data/gaia
tmp
.env
utils/__pycache__/

# Logs
*.log
logs/
log/

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
coverage.xml
*.cover

camel/types/__pycache__/
camel/__pycache__/
camel/utils/__pycache_/

data/*

