# Core dependencies
numpy>=1.26.0
openai>=1.59.7
tiktoken>=0.7.0
colorama>=0.4.6
jsonschema>=4.0.0
protobuf>=5.0.0
docstring-parser>=0.15.0
pydantic>=1.9.0,<2.10.0
eval-type-backport==0.2.0
curl_cffi==0.6.2
httpx>=0.28.0,<1.0.0
psutil>=5.9.8
pillow>=10.1.0,<11.0.0
retry>=0.9.2
loguru>=0.7.3
scenedetect>=0.6.5.2
openpyxl>=3.1.5
tabulate>=0.9.0
xls2xlsx>=0.2.0
docx2markdown>=0.1.1
chunkr_ai>=0.0.41
playwright>=1.50.0
html2text>=2024.2.26

# Optional dependencies - Model platforms
litellm>=1.38.1
mistralai>=1.1.0
reka-api>=3.0.8
anthropic>=0.42.0
cohere>=5.11.0
fish-audio-sdk>=2024.12.5

# Optional dependencies - Huggingface ecosystem
transformers>=4.0.0
diffusers>=0.25.0
accelerate>=0.26.0
datasets>=3.0.0
soundfile>=0.13.0
sentencepiece>=0.2.0
opencv-python>=4.0.0

# Optional dependencies - Core RAG components
sentence-transformers>=3.0.1
qdrant-client>=1.9.0
pymilvus>=2.4.0
rank-bm25>=0.2.2

# Optional dependencies - Storage solutions
neo4j>=5.18.0
nebula3-python==3.8.2
redis>=5.0.6
azure-storage-blob>=12.21.0
google-cloud-storage>=2.18.0
botocore>=1.35.3

# Optional dependencies - Document processing tools
beautifulsoup4>=4.0.0
docx2txt>=0.8.0
PyMuPDF>=1.22.5
unstructured==0.16.20
prance>=*********
openapi-spec-validator>=0.7.1
pandasai>=2.3.0

# Optional dependencies - Media processing tools
imageio[pyav]>=2.34.2
pydub>=0.25.1
yt-dlp>=2024.11.4
ffmpeg-python>=0.2.0

# Optional dependencies - Web and API tools
wikipedia>=1.0.0
linkup-sdk>=0.2.1
duckduckgo-search>=6.3.5
newspaper3k>=0.2.8
wolframalpha>=5.0.0
pyowm>=3.3.0
googlemaps>=4.10.0
requests_oauthlib>=1.3.1
firecrawl-py>=1.0.0
apify_client>=1.8.1
tavily-python>=0.5.0
dappier>=0.3.3
sympy>=1.13.3
mcp>=1.10.1
google-search-results>=2.4.2

# Optional dependencies - Communication platform tools
slack-sdk>=3.27.2
slack-bolt>=1.20.1
pygithub>=2.3.0
pyTelegramBotAPI>=4.18.0
discord.py>=2.3.2
notion-client>=2.2.1
praw>=7.7.1

# Optional dependencies - Data science and analytics tools
rouge>=1.0.1
aiosqlite>=0.20.0
textblob>=0.17.1
datacommons>=1.4.3
datacommons_pandas>=0.0.3
pandas>=1.5.3
stripe>=11.3.0
networkx>=3.4.2

# Optional dependencies - Research tools
scholarly[tor]==1.7.11
arxiv>=2.1.3
arxiv2text>=0.1.14

# Optional dependencies - Development tools
outlines>=0.1.7
docker>=7.1.0
jupyter_client>=8.6.2
ipykernel>=6.0.0
agentops>=0.3.21
e2b-code-interpreter>=1.0.3
tree-sitter-python>=0.23.6
tree-sitter>=0.23.2
pyyaml>=6.0.2

# Development and testing tools
pytest>=7.0.0
pytest-asyncio>=0.23.0
mock>=5.0.0
pytest-cov>=4.0.0
ruff>=0.7.0
mypy>=1.5.1
toml>=0.10.2
pre-commit>=3.0.0
gradio>=3.0.0

# Service dependencies
fastapi>=0.104.0
uvicorn>=0.24.0
python-multipart>=0.0.6

# Type stubs
types-Pillow
types-Pygments
types-mock
types-regex
types-setuptools
types-tqdm
types-colorama>=0.0.0
types-requests>=2.0.0
types-PyYAML>=6.0.0