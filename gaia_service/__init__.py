"""
GAIA Evaluation Service

An asynchronous service for evaluating GAIA benchmark queries with detailed execution traces.

This package provides:
- REST API endpoints for submitting and tracking GAIA evaluations
- Asynchronous processing using the existing GAIA workforce infrastructure
- Detailed execution traces capturing reasoning steps and tool usage
- Support for concurrent evaluation requests

Main Components:
- GAIAEvaluationService: Core service for processing evaluations
- GAIAServiceAPI: FastAPI application with REST endpoints
- TraceCollector: Execution trace collection and management
- ServiceConfig: Configuration management and environment setup

Usage:
    from gaia_service import GAIAEvaluationService, GAIAServiceAPI
    from gaia_service.workforce_factory import create_gaia_workforce
    
    # Create workforce and service
    workforce = create_gaia_workforce()
    service = GAIAEvaluationService(workforce)
    
    # Create API
    api = GAIAServiceAPI(service)
    app = api.app
"""

from .core import GAIAEvaluationService
from .api import GAIAServiceAPI
from .models import (
    GAIAQuery,
    EvaluationRequest,
    EvaluationResponse,
    EvaluationResult,
    ExecutionTrace,
    TaskStatus,
    TaskSubmissionResponse,
    TaskStatusResponse,
    ServiceHealth
)
from .config import ServiceConfig, load_config
from .workforce_factory import create_gaia_workforce, create_workforce_from_config
from .trace_collector import TraceCollector, WorkforceTraceCollector

__version__ = "1.0.0"
__author__ = "GAIA Service Team"
__email__ = "<EMAIL>"

__all__ = [
    # Core service
    "GAIAEvaluationService",
    "GAIAServiceAPI",
    
    # Models
    "GAIAQuery",
    "EvaluationRequest", 
    "EvaluationResponse",
    "EvaluationResult",
    "ExecutionTrace",
    "TaskStatus",
    "TaskSubmissionResponse",
    "TaskStatusResponse",
    "ServiceHealth",
    
    # Configuration
    "ServiceConfig",
    "load_config",
    
    # Workforce
    "create_gaia_workforce",
    "create_workforce_from_config",
    
    # Tracing
    "TraceCollector",
    "WorkforceTraceCollector",
]
