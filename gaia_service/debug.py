#!/usr/bin/env python3
"""Debug launcher for GAIA Service"""

import os
import sys
import subprocess

# Set debug environment
os.environ.update({
    "DEBUG": "true",
    "LOG_LEVEL": "DEBUG", 
    "HOST": "127.0.0.1",
    "PORT": "8000"
})

print("🐛 GAIA Service Debug Mode")
print("🌐 http://127.0.0.1:8000")
print("📚 http://127.0.0.1:8000/docs")

try:
    subprocess.run([sys.executable, "main.py"])
except KeyboardInterrupt:
    print("\n🛑 Stopped")
