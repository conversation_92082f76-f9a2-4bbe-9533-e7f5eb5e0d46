"""
GAIA Evaluation Service FastAPI Application

This module provides the REST API endpoints for the GAIA evaluation service,
including endpoints for submitting queries, checking status, and retrieving results.
"""

from datetime import datetime
from typing import Dict, List, Any

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger

from .core import GAIAEvaluationService
from .models import (
    EvaluationRequest,
    EvaluationResponse,
    TaskSubmissionResponse,
    TaskStatusResponse,
    ServiceHealth,
    TaskStatus,
    GAIAQuery
)


class GAIAServiceAPI:
    """FastAPI application for GAIA evaluation service."""
    
    def __init__(self, evaluation_service: GAIAEvaluationService):
        """
        Initialize the FastAPI application.
        
        Args:
            evaluation_service: The core GAIA evaluation service instance
        """
        self.service = evaluation_service
        self.app = FastAPI(
            title="GAIA Evaluation Service",
            description="Asynchronous service for evaluating GAIA benchmark queries",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Register routes
        self._register_routes()
    
    def _register_routes(self):
        """Register all API routes."""
        
        @self.app.get("/", response_model=Dict[str, str])
        async def root():
            """Root endpoint with service information."""
            return {
                "service": "GAIA Evaluation Service",
                "version": "1.0.0",
                "status": "running",
                "docs": "/docs"
            }
        
        @self.app.get("/health", response_model=ServiceHealth)
        async def health_check():
            """Get service health status."""
            stats = self.service.get_service_stats()
            
            return ServiceHealth(
                status="healthy",
                version="1.0.0",
                uptime_seconds=stats["uptime_seconds"],
                active_tasks=stats["active_tasks"],
                completed_tasks=stats["completed_tasks"],
                failed_tasks=stats["failed_tasks"],
                workforce_status="running" if stats.get("workforce_running", False) else "stopped"
            )
        
        @self.app.post("/evaluate", response_model=TaskSubmissionResponse)
        async def submit_evaluation(request: EvaluationRequest):
            """
            Submit a GAIA query for asynchronous evaluation.

            Args:
                request: The evaluation request containing query and parameters

            Returns:
                TaskSubmissionResponse with task_id for tracking
            """
            try:
                logger.info(f"Received evaluation request for question: {request.query.question[:100]}...")

                # Submit the evaluation
                task_id = await self.service.submit_evaluation(request)

                return TaskSubmissionResponse(
                    task_id=task_id,
                    status=TaskStatus.PENDING,
                    message="Evaluation task submitted successfully",
                    estimated_completion_time=None  # Could add estimation logic here
                )

            except Exception as e:
                logger.error(f"Error submitting evaluation: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to submit evaluation: {str(e)}"
                )

        @self.app.post("/tasks", response_model=TaskSubmissionResponse)
        async def submit_task(request: Dict[str, Any]):
            """
            Submit a GAIA task for evaluation (compatible with agent client).

            This endpoint provides compatibility with the agent client that expects
            a /tasks endpoint. It converts the request format to EvaluationRequest.

            Args:
                request: Task request in agent client format

            Returns:
                TaskSubmissionResponse with task_id for tracking
            """
            try:
                # Extract parameters from the request
                benchmark = request.get("benchmark", "gaia")
                model = request.get("model", "")
                params = request.get("params", {})

                # Validate that this is a GAIA request
                if benchmark != "gaia":
                    raise HTTPException(
                        status_code=400,
                        detail=f"This service only supports GAIA benchmark, got: {benchmark}"
                    )

                # Convert to EvaluationRequest format
                # Handle file attachments from file_name and file_path
                file_attachments = params.get("file_attachments", [])
                if params.get("file_name") and params.get("file_path"):
                    file_attachments.append(params["file_path"])

                gaia_query = GAIAQuery(
                    question=params.get("query", ""),
                    level=params.get("level", 1),
                    final_answer=params.get("final_answer", ""),  # Ground truth from client
                    file_attachments=file_attachments,
                    annotator_metadata=params.get("annotator_metadata", {}),
                    additional_info=params.get("additional_info", "")
                )

                # Handle LLM configuration from client
                llm_config = None
                if "llm_config" in request and request["llm_config"]:
                    from .models import LLMConfig
                    llm_config = LLMConfig(**request["llm_config"])

                evaluation_request = EvaluationRequest(
                    query=gaia_query,
                    max_tries=params.get("max_tries", 3),
                    max_replanning_tries=params.get("max_replanning_tries", 2),
                    timeout_seconds=params.get("timeout", 1800),
                    llm_config=llm_config
                )

                logger.info(f"Received task request for GAIA question: {gaia_query.question[:100]}...")

                # Submit the evaluation using the same service method
                task_id = await self.service.submit_evaluation(evaluation_request)

                return TaskSubmissionResponse(
                    task_id=task_id,
                    status=TaskStatus.PENDING,
                    message="GAIA evaluation task submitted successfully",
                    estimated_completion_time=None
                )

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error submitting task: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to submit task: {str(e)}"
                )
        
        @self.app.get("/status/{task_id}", response_model=TaskStatusResponse)
        async def get_task_status(task_id: str):
            """
            Get the current status of an evaluation task.
            
            Args:
                task_id: The unique task identifier
                
            Returns:
                TaskStatusResponse with current status information
            """
            try:
                response = await self.service.get_task_status(task_id)
                
                if not response:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Task {task_id} not found"
                    )
                
                # Calculate progress percentage based on status
                progress_map = {
                    TaskStatus.PENDING: 0.0,
                    TaskStatus.PROCESSING: 50.0,
                    TaskStatus.COMPLETED: 100.0,
                    TaskStatus.FAILED: 100.0
                }
                
                return TaskStatusResponse(
                    task_id=task_id,
                    status=response.status,
                    progress_percentage=progress_map.get(response.status, 0.0),
                    current_step=self._get_current_step(response),
                    estimated_remaining_time=None,  # Could add estimation logic
                    created_at=response.created_at,
                    last_updated=response.completed_at or response.created_at
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error getting task status: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to get task status: {str(e)}"
                )
        
        @self.app.get("/result/{task_id}", response_model=EvaluationResponse)
        async def get_evaluation_result(task_id: str):
            """
            Get the complete evaluation result including execution trace.
            
            Args:
                task_id: The unique task identifier
                
            Returns:
                Complete EvaluationResponse with results and trace
            """
            try:
                response = await self.service.get_evaluation_result(task_id)
                
                if not response:
                    # Check if task exists but not completed
                    status_response = await self.service.get_task_status(task_id)
                    if status_response:
                        raise HTTPException(
                            status_code=202,
                            detail=f"Task {task_id} is not yet completed. Current status: {status_response.status}"
                        )
                    else:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Task {task_id} not found"
                        )
                
                return response
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error getting evaluation result: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to get evaluation result: {str(e)}"
                )
        
        @self.app.get("/tasks/{task_id}")
        async def get_task_result(task_id: str):
            """
            Get task status and result (compatible with agent client).

            This endpoint provides compatibility with the agent client polling mechanism.
            It returns the task status and result in the format expected by the client.

            Args:
                task_id: The unique task identifier

            Returns:
                Task status and result in agent client format
            """
            try:
                # First check if task is completed
                evaluation_response = await self.service.get_evaluation_result(task_id)

                if evaluation_response and evaluation_response.status == TaskStatus.COMPLETED:
                    # Task is completed, return full result
                    result_data = {
                        "final_answer": "",
                        "trajectory": [],
                        "metrics": {},
                        "score": 0.0,
                        "is_correct": False
                    }

                    if evaluation_response.evaluation_result:
                        result_data.update({
                            "final_answer": evaluation_response.evaluation_result.model_answer,
                            "score": evaluation_response.evaluation_result.score,
                            "is_correct": evaluation_response.evaluation_result.correct
                        })

                    if evaluation_response.execution_trace:
                        # Convert execution trace to trajectory format
                        trajectory = []
                        for step in evaluation_response.execution_trace.steps:
                            trajectory.append({
                                "agent": step.agent_name,
                                "action": step.action_type,
                                "content": step.content,
                                "timestamp": step.timestamp.isoformat() if step.timestamp else None
                            })
                        result_data["trajectory"] = trajectory

                        # Add workforce trajectory if available
                        if evaluation_response.execution_trace.workforce_trajectory:
                            result_data["workforce_trajectory"] = evaluation_response.execution_trace.workforce_trajectory

                    return {
                        "task_id": task_id,
                        "status": "completed",
                        "result": result_data
                    }

                elif evaluation_response and evaluation_response.status == TaskStatus.FAILED:
                    # Task failed
                    return {
                        "task_id": task_id,
                        "status": "failed",
                        "error": evaluation_response.error_message or "Task failed"
                    }

                else:
                    # Task is still running or pending
                    status_response = await self.service.get_task_status(task_id)

                    if not status_response:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Task {task_id} not found"
                        )

                    # Map internal status to client expected status
                    status_map = {
                        TaskStatus.PENDING: "pending",
                        TaskStatus.PROCESSING: "running",
                        TaskStatus.COMPLETED: "completed",
                        TaskStatus.FAILED: "failed"
                    }

                    return {
                        "task_id": task_id,
                        "status": status_map.get(status_response.status, "unknown"),
                        "progress": status_response.progress_percentage or 0.0
                    }

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error getting task result: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to get task result: {str(e)}"
                )

        @self.app.get("/tasks", response_model=List[TaskStatusResponse])
        async def list_tasks(limit: int = 50, status: str = None):
            """
            List recent evaluation tasks.
            
            Args:
                limit: Maximum number of tasks to return
                status: Optional status filter
                
            Returns:
                List of TaskStatusResponse objects
            """
            try:
                # Get all tasks from service
                all_tasks = []
                
                # Add active tasks
                for task_id, response in self.service.active_tasks.items():
                    if not status or response.status == status:
                        all_tasks.append(self._create_status_response(task_id, response))
                
                # Add completed tasks
                for task_id, response in self.service.completed_tasks.items():
                    if not status or response.status == status:
                        all_tasks.append(self._create_status_response(task_id, response))
                
                # Sort by creation time (newest first) and limit
                all_tasks.sort(key=lambda x: x.created_at, reverse=True)
                return all_tasks[:limit]
                
            except Exception as e:
                logger.error(f"Error listing tasks: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to list tasks: {str(e)}"
                )
        
        @self.app.delete("/task/{task_id}")
        async def cancel_task(task_id: str):
            """
            Cancel a pending or running evaluation task.
            
            Args:
                task_id: The unique task identifier
                
            Returns:
                Success message
            """
            try:
                # Check if task exists and is cancellable
                response = await self.service.get_task_status(task_id)
                
                if not response:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Task {task_id} not found"
                    )
                
                if response.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Cannot cancel task {task_id} with status {response.status}"
                    )
                
                # Cancel the task (implementation would depend on how cancellation is handled)
                # For now, we'll just mark it as failed
                if task_id in self.service.active_tasks:
                    self.service.active_tasks[task_id].status = TaskStatus.FAILED
                    self.service.active_tasks[task_id].error_message = "Task cancelled by user"
                    self.service.active_tasks[task_id].completed_at = datetime.utcnow()
                
                return {"message": f"Task {task_id} cancelled successfully"}
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error cancelling task: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to cancel task: {str(e)}"
                )
    
    def _get_current_step(self, response: EvaluationResponse) -> str:
        """Get a human-readable description of the current step."""
        if response.status == TaskStatus.PENDING:
            return "Waiting to start"
        elif response.status == TaskStatus.PROCESSING:
            if response.execution_trace and response.execution_trace.steps:
                last_step = response.execution_trace.steps[-1]
                return f"{last_step.agent_name}: {last_step.action_type}"
            return "Processing"
        elif response.status == TaskStatus.COMPLETED:
            return "Completed"
        elif response.status == TaskStatus.FAILED:
            return "Failed"
        return "Unknown"
    
    def _create_status_response(self, task_id: str, response: EvaluationResponse) -> TaskStatusResponse:
        """Create a TaskStatusResponse from an EvaluationResponse."""
        progress_map = {
            TaskStatus.PENDING: 0.0,
            TaskStatus.PROCESSING: 50.0,
            TaskStatus.COMPLETED: 100.0,
            TaskStatus.FAILED: 100.0
        }
        
        return TaskStatusResponse(
            task_id=task_id,
            status=response.status,
            progress_percentage=progress_map.get(response.status, 0.0),
            current_step=self._get_current_step(response),
            estimated_remaining_time=None,
            created_at=response.created_at,
            last_updated=response.completed_at or response.created_at
        )
