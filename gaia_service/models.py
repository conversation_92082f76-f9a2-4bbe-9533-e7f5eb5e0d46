"""
GAIA Evaluation Service Models and Schemas

This module defines the Pydantic models for request/response schemas,
evaluation results, and execution traces for the GAIA evaluation service.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """Status of a GAIA evaluation task."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ToolCall(BaseModel):
    """Represents a tool call made during evaluation."""
    tool_name: str
    arguments: Dict[str, Any]
    result: Optional[Any] = None
    timestamp: datetime
    duration_ms: Optional[int] = None


class ExecutionStep(BaseModel):
    """Represents a single step in the execution trace."""
    step_id: int
    timestamp: datetime
    agent_name: str
    action_type: str  # e.g., "reasoning", "tool_call", "planning", "answer_generation"
    content: str
    tool_calls: List[ToolCall] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ExecutionTrace(BaseModel):
    """Detailed execution trace of the evaluation process."""
    task_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_duration_ms: Optional[int] = None
    steps: List[ExecutionStep] = Field(default_factory=list)
    workforce_trajectory: List[List[Dict[str, Any]]] = Field(default_factory=list)
    replanning_attempts: int = 0
    error_messages: List[str] = Field(default_factory=list)
    
    def add_step(self, agent_name: str, action_type: str, content: str, 
                 tool_calls: List[ToolCall] = None, metadata: Dict[str, Any] = None) -> ExecutionStep:
        """Add a new execution step to the trace."""
        step = ExecutionStep(
            step_id=len(self.steps) + 1,
            timestamp=datetime.utcnow(),
            agent_name=agent_name,
            action_type=action_type,
            content=content,
            tool_calls=tool_calls or [],
            metadata=metadata or {}
        )
        self.steps.append(step)
        return step


class EvaluationResult(BaseModel):
    """Result of a GAIA evaluation."""
    correct: bool
    model_answer: str
    ground_truth: str
    score: float  # 0.0 to 1.0
    confidence: Optional[float] = None  # Optional confidence score


class LLMConfig(BaseModel):
    """Configuration for LLM models."""
    model_platform: str = Field(description="Model platform (e.g., 'openai', 'anthropic', 'vllm')")
    model_type: str = Field(description="Model type (e.g., 'gpt-4o', 'claude-3-sonnet')")
    api_key: Optional[str] = Field(default=None, description="API key for the model service")
    url: Optional[str] = Field(default=None, description="Custom URL for the model service")
    model_config_dict: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional model configuration")
    timeout: Optional[float] = Field(default=None, description="Request timeout in seconds")


class GAIAQuery(BaseModel):
    """Input query for GAIA evaluation."""
    question: str
    level: int = Field(ge=1, le=3, description="GAIA difficulty level (1-3)")
    final_answer: str = Field(description="Expected correct answer")
    file_attachments: List[str] = Field(default_factory=list, description="List of file paths for attachments")
    annotator_metadata: Optional[Dict[str, Any]] = Field(default=None, description="Optional metadata from annotators")
    additional_info: str = Field(default="", description="Additional context or information")


class EvaluationRequest(BaseModel):
    """Request to evaluate a GAIA query."""
    query: GAIAQuery
    max_tries: int = Field(default=3, ge=1, le=10, description="Maximum number of retry attempts")
    max_replanning_tries: int = Field(default=2, ge=0, le=5, description="Maximum replanning attempts")
    timeout_seconds: int = Field(default=1800, ge=60, le=3600, description="Timeout in seconds")
    llm_config: Optional[LLMConfig] = Field(default=None, description="LLM configuration for the evaluation")


class EvaluationResponse(BaseModel):
    """Response containing evaluation results and execution trace."""
    task_id: str
    status: TaskStatus
    evaluation_result: Optional[EvaluationResult] = None
    execution_trace: Optional[ExecutionTrace] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


class TaskSubmissionResponse(BaseModel):
    """Response when submitting a task for evaluation."""
    task_id: str
    status: TaskStatus
    message: str
    estimated_completion_time: Optional[datetime] = None


class TaskStatusResponse(BaseModel):
    """Response for task status queries."""
    task_id: str
    status: TaskStatus
    progress_percentage: Optional[float] = None
    current_step: Optional[str] = None
    estimated_remaining_time: Optional[int] = None  # seconds
    created_at: datetime
    last_updated: datetime


class ServiceHealth(BaseModel):
    """Service health status."""
    status: str
    version: str
    uptime_seconds: int
    active_tasks: int
    completed_tasks: int
    failed_tasks: int
    workforce_status: str


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    message: str
    task_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
