#===========================================
# MODEL & API 
# (See https://docs.camel-ai.org/key_modules/models.html#)
#===========================================

# OPENAI API (https://platform.openai.com/api-keys)
OPENAI_API_KEY="http://35.220.164.252:3888/v1/"
OPENAI_API_BASE_URL="sk-jiH0b552VZiiOWVGeT2RJ5HuwPJmwSyQvHaeDIYAlH2w0EiS"


# Qwen API (https://help.aliyun.com/zh/model-studio/developer-reference/get-api-key)
# QWEN_API_KEY="Your_Key"
# QWEN_API_BASE_URL=""

# DeepSeek API (https://platform.deepseek.com/api_keys)
# DEEPSEEK_API_KEY="Your_Key"
# DEEPSEEK_API_BASE_URL=""

# PPIO API (https://ppinfra.com/settings/key-management?utm_source=github_owl)
# PPIO_API_KEY="Your_Key"

# GROQ API (https://console.groq.com/)
# GROQ_API_KEY="Your_Key"
# GROQ_API_BASE_URL=""

# Azure OpenAI API
# AZURE_OPENAI_BASE_URL=""
# AZURE_API_VERSION=""
# AZURE_OPENAI_API_KEY="Your_Key"
# AZURE_DEPLOYMENT_NAME=""

#GOOGLE GEMINI API (https://ai.google.dev/gemini-api/docs/api-key)
# GEMINI_API_KEY ="Your_Key"

# NOVITA API (https://novita.ai/settings/key-management?utm_source=github_owl&utm_medium=github_readme&utm_campaign=github_link)
# NOVITA_API_KEY="Your_Key"

#===========================================
# Tools & Services API
#===========================================

# Google Search API (https://coda.io/@jon-dallas/google-image-search-pack-example/search-engine-id-and-google-api-key-3)
GOOGLE_API_KEY="AIzaSyBLe2uhQyqN9EmJpAr2oVsBumJQ5aK5nEw"
SEARCH_ENGINE_ID="863f90bbc86514e96"

# Chunkr API (https://chunkr.ai/)
CHUNKR_API_KEY="ch_Ji7w4nvcKVt_6N3AZxfmrDNisJCA7eRF11qch6qu4rPSt"

# Firecrawl API (https://www.firecrawl.dev/)
FIRECRAWL_API_KEY="fc-19cdc4e9443c4f00ad65f3b6f795ff2a"
#FIRECRAWL_API_URL="https://api.firecrawl.dev"
